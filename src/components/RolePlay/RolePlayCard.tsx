import React, {useMemo} from 'react';
import {TouchableOpacity, View} from 'react-native';
import Svg, {G, Image, Path} from 'react-native-svg';

// Try Skia first, fallback to SVG
let Canvas, SkiaImage, useImage, Path as SkiaPath, Group;
let SkiaAvailable = false;

try {
  const SkiaModule = require('@shopify/react-native-skia');
  Canvas = SkiaModule.Canvas;
  SkiaImage = SkiaModule.Image;
  useImage = SkiaModule.useImage;
  SkiaPath = SkiaModule.Path;
  Group = SkiaModule.Group;
  SkiaAvailable = true;
} catch (error) {
  SkiaAvailable = false;
}

type Props = {
  color: string;
  href: string;
  onPressCard?: () => void;
};

// Skia version - High performance
const RolePlayCardSkia = ({color, href, onPressCard}: Props) => {
  const characterImage = useImage(href);

  // Pre-computed card path (simplified from complex SVG)
  const cardPath = useMemo(() => {
    if (!SkiaPath) return null;

    // Simplified card shape path
    const path = SkiaPath.Make();
    path.addRoundRect({x: 8, y: 7, width: 70, height: 90}, 4, 4);
    return path;
  }, []);

  if (!characterImage || !cardPath) {
    return null;
  }

  return (
    <TouchableOpacity onPress={onPressCard}>
      <Canvas style={{width: 87, height: 108}}>
        {/* Card background */}
        <SkiaPath path={cardPath} color={color} />

        {/* Character image */}
        <SkiaImage
          image={characterImage}
          x={20}
          y={16}
          width={47}
          height={76}
          fit="contain"
        />
      </Canvas>
    </TouchableOpacity>
  );
};

// SVG version (Original)
const RolePlayCardSVG = ({color, href, onPressCard}: Props) => {
  return (
    <TouchableOpacity onPress={onPressCard}>
      <Svg fill="none" width={87} height={108} viewBox="0 0 87 108">
        <G
          opacity={0.5}
          style={{
            mixBlendMode: 'multiply',
          }}>
          <Path
            fill="#BD8B01"
            d="M83.467 101.919c-1.183-9.38-.84-19.028-.884-28.519-.042-17.065-.067-34.136-.185-51.194-.754-14.212 5.795-16.664-11.625-15.544-15.97-.115-31.62-.096-47.538-.178C-.37 7.145 4.036.91 4.329 21.516c.035 19.768.216 39.457.337 59.244-.127 4.6.293 9.275-.127 13.9-.054 3.712-2.923 10.198 3.457 9.361 17.583-.995 35.051-.194 52.694-.617 4.64.175 9.26-.07 13.95.086 2.679-.188 7.714 1.765 8.824-1.543v-.028h.003Z"
          />
          <Path
            stroke="#BD8B01"
            strokeMiterlimit={10}
            strokeWidth={6}
            d="M83.467 101.919c-1.183-9.38-.84-19.028-.884-28.519-.042-17.065-.067-34.136-.185-51.194-.754-14.212 5.795-16.664-11.625-15.544-15.97-.115-31.62-.096-47.538-.178C-.37 7.145 4.036.91 4.329 21.516c.035 19.768.216 39.457.337 59.244-.127 4.6.293 9.275-.127 13.9-.054 3.712-2.923 10.198 3.457 9.361 17.583-.995 35.051-.194 52.694-.617 4.64.175 9.26-.07 13.95.086 2.679-.188 7.714 1.765 8.824-1.543v-.028h.003Z"
          />
        </G>
        <Path
          fill="#8D6A00"
          stroke="#7F4B00"
          strokeMiterlimit={10}
          strokeWidth={6}
          d="M83.467 99.257c-1.183-9.38-.84-19.028-.884-28.52-.042-17.064-.067-34.135-.185-51.193C81.644 5.332 88.193 2.88 70.773 3.999c-15.97-.114-31.62-.095-47.538-.178C-.37 4.483 4.036-1.75 4.329 18.854c.035 19.768.216 39.457.337 59.244-.127 4.6.293 9.275-.127 13.9-.054 3.712-2.923 10.198 3.457 9.361 17.583-.996 35.051-.194 52.694-.617 4.64.175 9.26-.07 13.95.086 2.679-.188 7.714 1.765 8.824-1.543v-.028h.003Z"
        />
        <Path fill={color} d="M78.613 7.266H8.581v90.168h70.032V7.266Z" />
        <Path
          fill="#B1BA00"
          d="M7.16 101.327c.241-.006.486-.016.725-.06.731-.143 1.304-.633 1.8-1.129.162-.166.334-.376.251-.583-.038-.095-.127-.168-.2-.248a1.374 1.374 0 0 1-.213-.314c.083-1.136-.032-3.687-.15-5.872.08-.111.156-.22.223-.337.165-.29.251-.646.08-.932-.086-.147-.233-.258-.354-.382-.015-.016-.031-.032-.044-.05-.05-.914-.09-1.614-.09-1.887-.005-2.452-.009-4.905-.015-7.35l.49-.462c.105-.099.213-.2.257-.33.074-.195-.012-.411-.143-.583-.13-.169-.305-.308-.448-.47a1.25 1.25 0 0 1-.162-.23l-.029-12.157.42-.422c.207-.207.423-.449.397-.723-.028-.273-.289-.473-.515-.661a1.462 1.462 0 0 1-.305-.321c-.01-4.47-.02-8.929-.032-13.385l.372-.382a.922.922 0 0 0 .153-.184.42.42 0 0 0-.013-.436c-.035-.057-.086-.105-.13-.153a3.52 3.52 0 0 1-.385-.49c-.01-3.76-.016-7.512-.026-11.262.179-.16.379-.306.506-.497a.155.155 0 0 0 .035-.085c0-.055-.041-.102-.08-.144a5.825 5.825 0 0 1-.464-.55l-.028-12.602c.21-.175.413-.36.6-.553.023-.026.048-.051.052-.083a.148.148 0 0 0-.02-.064c-.152-.308-.435-.553-.636-.836-.006-3.22-.015-6.432-.022-9.641.146-.188.312-.366.496-.528.08-.07.163-.14.198-.232.035-.093.012-.191-.02-.284-.133-.362-.47-.636-.674-.97 0-1.39-.006-2.78-.01-4.17 0-1.332.548-2.665-.511-3.355-.812-.528-3.194-.471-3.591.346-.401.821.32 1.89.327 2.923.01 1.368.016 2.74.026 4.11-.185.127-.395.242-.449.436a.451.451 0 0 0 .064.347c.063.104.152.197.241.286l.153.152.067 10.538a.742.742 0 0 0-.165.312c-.02.08-.02.162-.02.245 0 .12 0 .251.064.36.032.053.08.101.13.142.023 3.372.042 6.744.064 10.122.006.919.013 1.838.016 2.757l-.312.277c-.054.048-.108.095-.14.156a.46.46 0 0 0-.038.235c.003.182.026.363.102.531.083.188.226.347.369.503l.029.032c.022 3.763.047 7.529.07 11.301-.175.197-.414.372-.493.61-.112.335.117.678.337.97.057.077.114.15.168.227.029 4.494.057 8.995.083 13.502-.08.086-.162.169-.242.254-.187.198-.388.44-.318.688.054.197.26.33.455.438.038.02.076.042.114.064l.02 2.898c.022 3.422.04 6.851.063 10.28l-.36.334a.424.424 0 0 0-.11.14.275.275 0 0 0 0 .175c.054.19.206.346.372.48.035.029.07.054.105.083l.038 6.364c.006 1.145.066 2.297.006 3.464-.169.083-.36.143-.515.248a.814.814 0 0 0-.353.611.965.965 0 0 0 .229.665c.146.178.346.324.451.525a.506.506 0 0 1 .032.073c-.102 1.568-.232 4.01-.207 5.204a.629.629 0 0 0-.178.512c.023.219.134.423.261.614.245.365.582.725 1.05.83.21.047.432.041.649.035v-.01Z"
        />
        <G fill="#8D6A00">
          <Path d="M5.43 81.657c.295.544 3.11.169 3.724-.098.417-.172.7-.614.213-.78-.213-.076-.496-.054-.747-.022-.72.096-1.45.191-2.182.293-.27.035-1.142.204-1.012.601v.006h.003ZM9.545 66.858c0-.163-.098-.347-.184-.468-.09-.124-.233-.083-.382-.07-.477.054-1.005.143-1.479.207-.449.095-2.045.08-2.316.442-.028.108.121.197.207.245.818.454 1.991.467 2.923.296.255-.032 1.177-.328 1.228-.643v-.01h.003ZM5.299 24.439c.29-.544 3.079-.27 3.686-.029.414.153.697.58.217.757-.213.083-.49.07-.738.048-.713-.07-1.438-.137-2.163-.21-.27-.025-1.132-.162-1.005-.557v-.006l.003-.003ZM4.825 12.67c.321-.515 3.41.029 4.08.328.459.194.767.645.236.776-.235.06-.54.022-.817-.026a479.16 479.16 0 0 1-2.392-.42c-.3-.05-1.256-.27-1.117-.651v-.007h.01ZM9.462 38.803c0 .16-.101.344-.19.468-.096.127-.242.089-.398.082-.5-.038-1.053-.11-1.549-.159-.47-.08-2.144-.016-2.427-.369-.032-.105.127-.197.213-.248.856-.474 2.084-.524 3.06-.381.264.025 1.234.29 1.288.598v.01h.003ZM9.545 51.691c.029-.155-.111-.327-.305-.394-.194-.07-.398-.048-.601-.035-.344.026-.668.051-1.047.083-.528.044-1.218.102-1.727.2-.254.06-.557.096-.544.35.175.62 2.834.165 3.4.156.261-.045.71-.022.82-.347v-.01l.004-.003ZM9.606 91.915c0-.11-.077-.165-.175-.168-.315-.013-.595.28-.916.487-.34.222-.716.422-1.088.505-.312.07-.608.035-.856-.067-.267-.108-.591-.302-.89-.197-.64.27-.261.875.127.977.875.308 2.077-.023 3.015-.64.3-.213.725-.509.776-.884v-.01l.007-.003ZM8.607 4.833a1.57 1.57 0 0 0-.604-.27c-.751-.182-1.559-.112-2.297.114-.27.083-.54.188-.738.372-.197.184-.308.455-.213.693a.28.28 0 0 0 .057.093c.06.057.163.063.248.044.086-.019.16-.067.233-.108.518-.293 1.141-.423 1.758-.407a4.762 4.762 0 0 1 1.772.41c.076.035.166.07.245.048a.222.222 0 0 0 .076-.041c.357-.277-.302-.78-.537-.945v-.003Z" />
        </G>
        <Path
          fill="#B1BA00"
          d="M80.369 101.327c.242-.006.487-.016.725-.06.732-.143 1.304-.633 1.8-1.129.163-.166.334-.376.252-.583-.038-.095-.128-.168-.2-.248a1.37 1.37 0 0 1-.214-.314c.083-1.136-.032-3.687-.15-5.872.08-.111.157-.22.223-.337.166-.29.252-.646.08-.932-.086-.147-.232-.258-.353-.382-.016-.016-.032-.032-.045-.05a53.94 53.94 0 0 1-.089-1.887l-.016-7.35.49-.462c.105-.099.213-.2.258-.33.073-.195-.013-.411-.143-.583-.13-.169-.306-.308-.449-.47a1.254 1.254 0 0 1-.162-.23l-.029-12.157.42-.422c.207-.207.423-.449.398-.723-.029-.273-.29-.473-.516-.661a1.462 1.462 0 0 1-.305-.321c-.01-4.47-.019-8.929-.032-13.385l.372-.382a.918.918 0 0 0 .153-.184.42.42 0 0 0-.013-.436c-.035-.057-.085-.105-.13-.153a3.529 3.529 0 0 1-.385-.49c-.01-3.76-.016-7.512-.025-11.262.178-.16.378-.306.505-.497a.155.155 0 0 0 .035-.085c0-.055-.041-.102-.08-.144a5.816 5.816 0 0 1-.463-.55l-.03-12.602c.21-.175.414-.36.602-.553.022-.026.048-.051.05-.083a.147.147 0 0 0-.018-.064c-.153-.308-.436-.553-.636-.836l-.023-9.641c.147-.188.312-.366.497-.528.08-.07.162-.14.197-.232.035-.093.013-.191-.02-.284-.133-.362-.47-.636-.674-.97 0-1.39-.006-2.78-.01-4.17 0-1.332.548-2.665-.511-3.355-.811-.528-3.194-.471-3.591.346-.4.821.321 1.89.327 2.923.01 1.368.016 2.74.026 4.11-.185.127-.394.242-.449.436a.451.451 0 0 0 .064.347c.064.104.153.197.242.286l.152.152.067 10.538a.743.743 0 0 0-.165.312c-.02.08-.02.162-.02.245 0 .12 0 .251.064.36.032.053.08.101.13.142.023 3.372.042 6.744.064 10.122.007.919.013 1.838.016 2.757-.105.092-.207.185-.311.277a.587.587 0 0 0-.14.156.461.461 0 0 0-.038.235c.003.182.025.363.101.531.083.188.226.347.37.503l.028.032c.022 3.763.047 7.529.07 11.301-.175.197-.414.372-.493.61-.111.335.118.678.337.97.057.077.115.15.169.227.028 4.494.057 8.995.082 13.502-.08.086-.162.169-.242.254-.187.198-.388.44-.318.688.055.197.261.33.455.438.038.02.077.042.115.064l.019 2.898c.022 3.422.041 6.851.064 10.28l-.36.334a.425.425 0 0 0-.111.14.275.275 0 0 0 0 .175c.054.19.206.346.372.48.035.029.07.054.105.083l.038 6.364c.006 1.145.067 2.297.006 3.464-.168.083-.36.143-.515.248a.814.814 0 0 0-.353.611.965.965 0 0 0 .229.665c.146.178.347.324.452.525a.505.505 0 0 1 .031.073c-.101 1.568-.232 4.01-.206 5.204a.629.629 0 0 0-.178.512c.022.219.133.423.26.614.245.365.582.725 1.05.83.21.047.433.041.649.035v-.01Z"
        />
        <G fill="#8D6A00">
          <Path d="M78.639 81.657c.295.544 3.11.169 3.724-.098.417-.172.7-.614.213-.78-.213-.076-.496-.054-.747-.022-.719.096-1.45.191-2.182.293-.27.035-1.142.204-1.012.601v.006h.004ZM82.754 66.858c0-.163-.098-.347-.184-.468-.09-.124-.232-.083-.382-.07-.477.054-1.005.143-1.479.207-.448.095-2.045.08-2.315.442-.029.108.12.197.206.245.818.454 1.992.467 2.924.296.254-.032 1.176-.328 1.227-.643v-.01h.003ZM78.508 24.439c.29-.544 3.08-.27 3.687-.029.413.153.696.58.216.757-.213.083-.49.07-.738.048-.713-.07-1.438-.137-2.163-.21-.27-.025-1.132-.162-1.005-.557v-.006l.003-.003ZM78.034 12.67c.322-.515 3.41.029 4.081.328.458.194.767.645.236.776-.236.06-.541.022-.818-.026a476.56 476.56 0 0 1-2.392-.42c-.299-.05-1.256-.27-1.116-.651v-.007h.01ZM82.672 38.803c0 .16-.102.344-.191.468-.096.127-.242.089-.398.082-.5-.038-1.052-.11-1.549-.159-.47-.08-2.143-.016-2.427-.369-.031-.105.128-.197.214-.248.855-.474 2.083-.524 3.06-.381.263.025 1.234.29 1.288.598v.01h.003ZM82.758 51.691c.028-.155-.112-.327-.306-.394-.194-.07-.397-.048-.6-.035-.344.026-.669.051-1.047.083-.528.044-1.219.102-1.727.2-.255.06-.557.096-.544.35.175.62 2.834.165 3.4.156.26-.045.71-.022.82-.347v-.01l.004-.003ZM82.815 91.915c0-.11-.076-.165-.175-.168-.315-.013-.595.28-.916.487-.34.222-.716.422-1.088.505a1.42 1.42 0 0 1-.855-.067c-.268-.108-.592-.302-.891-.197-.64.27-.26.875.127.977.875.308 2.077-.023 3.015-.64.3-.213.726-.509.776-.884v-.01l.007-.003ZM81.82 4.833a1.57 1.57 0 0 0-.605-.27c-.75-.182-1.559-.112-2.297.114-.27.083-.54.188-.737.372-.198.184-.309.455-.213.693a.28.28 0 0 0 .057.093c.06.057.162.063.248.044.086-.019.159-.067.232-.108.519-.293 1.142-.423 1.759-.407a4.762 4.762 0 0 1 1.772.41c.076.035.165.07.245.048a.222.222 0 0 0 .076-.041c.356-.277-.302-.78-.538-.945v-.003Z" />
        </G>
        <Path
          fill="#B1BA00"
          d="M4.097 6.286c.006-.241.012-.486.05-.725.118-.731.519-1.304.923-1.8.134-.162.308-.334.474-.252.08.039.137.128.203.2.08.09.166.157.258.214.929-.083 3.009.032 4.793.15.09-.08.182-.156.274-.223.235-.166.525-.251.76-.08.118.086.21.233.312.353a.25.25 0 0 0 .041.045c.744.05 1.317.089 1.54.089l6.001.016c.128-.162.252-.328.379-.49.08-.105.165-.213.27-.258.16-.073.337.013.474.144.14.13.252.305.385.448.057.064.12.118.188.162l9.923.029.347-.42c.169-.207.366-.423.592-.398.222.029.388.29.54.516.077.114.166.226.261.305 3.648.01 7.287.02 10.926.032.105-.124.206-.248.311-.372a.77.77 0 0 1 .153-.153.291.291 0 0 1 .356.013c.045.035.086.086.124.13.125.143.261.274.401.385 3.07.01 6.133.016 9.195.025.13-.178.249-.378.404-.505.023-.02.045-.035.07-.035.045 0 .083.041.118.08.143.168.293.32.449.464l10.286.028c.143-.21.292-.413.451-.6.02-.023.042-.049.067-.052a.1.1 0 0 1 .051.02c.251.152.452.435.684.635L75 4.43c.153-.147.299-.312.433-.496.057-.08.114-.163.187-.198s.156-.012.232.02c.296.133.519.47.792.674 1.136 0 2.268.006 3.404.01 1.087 0 2.175-.548 2.738.511.433.811.382 3.194-.283 3.591-.668.401-1.543-.32-2.385-.327-1.117-.01-2.236-.016-3.356-.026-.105.185-.197.395-.356.449-.096.032-.197 0-.283-.064a1.417 1.417 0 0 1-.236-.242l-.124-.152-8.6-.067a.578.578 0 0 1-.255.165c-.066.02-.133.02-.2.02-.099 0-.204 0-.293-.064a.52.52 0 0 1-.117-.13c-2.752-.023-5.506-.042-8.26-.064-.751-.006-1.502-.013-2.252-.016-.077.105-.153.207-.226.312a.546.546 0 0 1-.128.14.32.32 0 0 1-.194.038.91.91 0 0 1-.432-.102c-.153-.083-.283-.226-.41-.369a.21.21 0 0 1-.026-.029c-3.072-.022-6.145-.047-9.224-.07-.162.175-.305.414-.5.493-.273.112-.553-.117-.791-.337a8.836 8.836 0 0 0-.184-.168c-3.668-.029-7.345-.058-11.021-.083-.07.08-.14.162-.207.242-.162.187-.356.388-.563.318-.162-.054-.27-.261-.36-.455-.016-.038-.035-.076-.05-.115l-2.364-.019c-2.792-.022-5.591-.04-8.39-.063-.093.12-.182.238-.274.36a.38.38 0 0 1-.114.11c-.045.023-.096.016-.144 0-.155-.054-.283-.206-.39-.372l-.068-.105-5.197-.038c-.935-.006-1.876-.067-2.827-.006-.07.168-.118.36-.204.515-.118.21-.308.337-.5.353-.19.016-.384-.073-.543-.229-.146-.146-.264-.346-.426-.451a.363.363 0 0 0-.06-.032c-1.283.102-3.277.232-4.247.207a.458.458 0 0 1-.417.178c-.178-.023-.346-.134-.502-.261-.3-.245-.592-.582-.678-1.05a3.15 3.15 0 0 1-.028-.649Z"
        />
        <G fill="#8D6A00">
          <Path d="M20.153 8.017c-.446-.296-.137-3.111.082-3.725.14-.417.503-.7.637-.213.06.213.044.496.019.747-.077.72-.156 1.45-.239 2.182-.029.27-.165 1.142-.493 1.012h-.006v-.003ZM32.233 3.9c.13 0 .283.1.382.185.101.09.066.232.057.382-.045.477-.115 1.005-.169 1.479-.076.448-.063 2.045-.362 2.315-.09.03-.16-.12-.2-.206-.37-.818-.383-1.991-.243-2.923.026-.255.268-1.177.525-1.228h.01V3.9ZM66.858 8.147c.442-.29.222-3.079.025-3.686-.124-.414-.474-.697-.617-.217-.067.213-.057.49-.038.738.057.713.111 1.438.172 2.163.022.27.13 1.132.454 1.005h.007l-.004-.003ZM76.466 8.624c.42-.321-.022-3.41-.267-4.08-.156-.459-.525-.767-.633-.236-.05.235-.016.54.02.817.11.79.225 1.59.343 2.392.041.3.222 1.257.534 1.117h.006l-.003-.01ZM55.13 3.987c-.13 0-.283.101-.381.19-.105.096-.07.242-.067.398.032.5.089 1.053.13 1.549.067.47.013 2.144.303 2.427.085.032.162-.127.203-.213.388-.856.426-2.084.312-3.06-.02-.264-.236-1.234-.487-1.288h-.006l-.007-.003ZM44.612 3.9c.127-.028.267.112.321.306.057.194.038.398.029.601l-.067 1.047c-.035.528-.083 1.218-.162 1.727-.048.254-.08.556-.283.544-.506-.175-.134-2.834-.128-3.4.039-.261.02-.71.284-.821h.006V3.9ZM11.778 3.84c.089 0 .133.077.137.175.013.315-.23.595-.398.916-.181.34-.346.716-.413 1.088-.058.312-.029.608.054.856.089.267.245.591.162.89-.22.64-.713.261-.798-.127-.252-.875.019-2.077.521-3.015.175-.3.417-.725.722-.776h.01l.003-.007ZM82.86 4.839a1.748 1.748 0 0 1 .219.604c.15.751.089 1.559-.092 2.297-.067.27-.156.54-.303.738-.146.197-.372.308-.566.213a.209.209 0 0 1-.073-.057c-.044-.06-.054-.163-.035-.248.02-.086.054-.16.09-.233.238-.518.343-1.142.333-1.759a5.56 5.56 0 0 0-.334-1.771c-.028-.077-.057-.166-.038-.245a.277.277 0 0 1 .032-.077c.226-.356.636.303.77.538h-.004Z" />
        </G>
        <Path
          fill="#8D6A00"
          d="M82.408 8.373c.213.118.445.242.566.455.12.213.06.547-.175.614a.576.576 0 0 1-.302-.02c-.974-.254-1.842-.833-2.57-1.526-.729-.694-1.333-1.508-1.928-2.322-.2-.274-.404-.56-.464-.894-.086-.48.41-1.23.916-.805.169.143.2.49.274.684.127.328.302.62.499.91a9.402 9.402 0 0 0 3.18 2.904h.004Z"
        />
        <Path
          fill="#8D6A00"
          d="M80.483 6.391c-.203.385-.413.773-.747 1.1-.379.376-.916.656-1.51.793-.166.197.155.458.46.442.306-.016.55-.194.76-.363.424-.337.85-.677 1.2-1.059.862-.941 1.23-2.109 1.256-3.254.003-.162-.547.045-.604.099-.147.14-.096.432-.13.598-.115.572-.405 1.113-.688 1.647l.003-.003ZM7.653 6.366c-.458-.75-.767-1.568-1.03-2.392-.045-.137-.093-.28-.201-.382-.108-.102-.306-.143-.423-.05-.2.655.15 1.335.493 1.94.486.852.995 1.717 1.746 2.395.162.146.582.48.843.407.41-.115-.06-.353-.226-.49a5.779 5.779 0 0 1-1.206-1.425l.004-.003Z"
        />
        <Path
          fill="#8D6A00"
          d="M8.613 4.562a10.817 10.817 0 0 1-3.55 3.063c-.244.134-.54.34-.473.611.044.188.257.293.448.28.191-.013.366-.111.528-.21 1.123-.684 2.188-1.479 3.066-2.459.43-.483.904-1.097.964-1.765.108-1.16-.792.223-.983.48Z"
        />
        <Path
          fill="#B1BA00"
          d="M4.097 98.553c.006.242.012.487.05.726.118.731.519 1.304.923 1.8.134.162.308.334.474.251.08-.038.137-.127.203-.2a1.16 1.16 0 0 1 .258-.213c.929.082 3.009-.032 4.793-.15.09.08.182.156.274.223.235.165.525.251.76.079.118-.085.21-.232.312-.353a.282.282 0 0 1 .041-.044 35.63 35.63 0 0 1 1.54-.089c2.003-.007 4-.01 6.001-.016.128.162.252.328.379.49.08.105.165.213.27.257.16.074.337-.012.474-.143.14-.13.252-.305.385-.448a.96.96 0 0 1 .188-.162l9.923-.029.347.42c.169.207.366.423.592.397.222-.028.388-.289.54-.515a1.48 1.48 0 0 1 .261-.305c3.648-.01 7.287-.019 10.926-.032.105.124.206.248.311.372.048.057.096.115.153.153a.292.292 0 0 0 .356-.013c.045-.035.086-.086.124-.13.125-.143.261-.274.401-.385 3.07-.01 6.133-.016 9.195-.026.13.178.249.379.404.506.023.019.045.035.07.035.045 0 .083-.041.118-.079.143-.169.293-.322.449-.465l10.286-.029c.143.21.292.414.451.602.02.022.042.047.067.051.02 0 .035-.01.051-.02.251-.152.452-.435.684-.636 2.627-.006 5.251-.016 7.869-.022.153.146.299.312.433.496.057.08.114.162.187.197s.156.013.232-.019c.296-.133.519-.47.792-.674 1.136 0 2.268-.006 3.404-.009 1.087 0 2.175.547 2.738-.513.433-.81.382-3.193-.283-3.59-.668-.402-1.543.32-2.385.327-1.117.01-2.236.016-3.356.025-.105-.184-.197-.394-.356-.448-.096-.032-.197 0-.283.064a1.415 1.415 0 0 0-.236.241l-.124.153-8.6.067a.578.578 0 0 0-.255-.166.718.718 0 0 0-.2-.019c-.099 0-.204 0-.293.064a.52.52 0 0 0-.117.13c-2.752.023-5.506.042-8.26.064-.751.006-1.502.013-2.252.016-.077-.105-.153-.207-.226-.312a.545.545 0 0 0-.128-.14.321.321 0 0 0-.194-.038.91.91 0 0 0-.432.102c-.153.082-.283.226-.41.369-.01.01-.02.019-.026.028-3.072.023-6.145.048-9.224.07-.162-.175-.305-.413-.5-.493-.273-.111-.553.118-.791.337-.06.058-.124.115-.184.17-3.668.028-7.345.056-11.021.082-.07-.08-.14-.162-.207-.242-.162-.188-.356-.388-.563-.318-.162.054-.27.26-.36.455-.016.038-.035.076-.05.114l-2.364.02c-2.792.022-5.591.04-8.39.063-.093-.12-.182-.239-.274-.36a.38.38 0 0 0-.114-.11c-.045-.023-.096-.017-.144 0-.155.053-.283.206-.39.371l-.068.105-5.197.038c-.935.007-1.876.067-2.827.007-.07-.169-.118-.36-.204-.515-.118-.21-.308-.338-.5-.353-.19-.016-.384.073-.543.229-.146.146-.264.346-.426.451a.362.362 0 0 1-.06.032c-1.283-.102-3.277-.232-4.247-.207a.458.458 0 0 0-.417-.178c-.178.023-.346.134-.502.261-.3.245-.592.582-.678 1.05a3.15 3.15 0 0 0-.028.648Z"
        />
        <G fill="#8D6A00">
          <Path d="M20.153 96.826c-.446.296-.137 3.111.082 3.725.14.417.503.7.637.213.06-.213.044-.496.019-.747-.077-.72-.156-1.45-.239-2.182-.029-.27-.165-1.142-.493-1.012h-.006v.003ZM32.233 100.939c.13 0 .283-.099.382-.184.101-.089.066-.233.057-.382-.045-.477-.115-1.005-.169-1.48-.076-.448-.063-2.044-.362-2.315-.09-.028-.16.121-.2.207-.37.818-.383 1.991-.243 2.923.026.255.268 1.177.525 1.228h.01v.003ZM66.858 96.693c.442.29.222 3.079.025 3.686-.124.414-.474.697-.617.217-.067-.214-.057-.49-.038-.738.057-.713.111-1.438.172-2.163.022-.27.13-1.133.454-1.005h.007l-.004.003ZM76.466 96.219c.42.321-.022 3.41-.267 4.081-.156.458-.525.766-.633.235-.05-.235-.016-.54.02-.817.11-.79.225-1.59.343-2.392.041-.3.222-1.257.534-1.117h.006l-.003.01ZM55.13 100.856c-.13 0-.283-.101-.381-.19-.105-.096-.07-.242-.067-.398.032-.5.089-1.053.13-1.55.067-.47.013-2.143.303-2.426.085-.032.162.127.203.213.388.856.426 2.084.312 3.06-.02.264-.236 1.234-.487 1.288h-.006l-.007.003ZM44.612 100.942c.127.029.267-.111.321-.305.057-.194.038-.398.029-.601l-.067-1.047c-.035-.528-.083-1.218-.162-1.727-.048-.254-.08-.556-.283-.544-.506.175-.134 2.834-.128 3.4.039.261.02.71.284.821h.006v.003ZM11.778 100.999c.089 0 .133-.076.137-.174.013-.315-.23-.595-.398-.916-.181-.34-.346-.716-.413-1.088a1.728 1.728 0 0 1 .054-.856c.089-.267.245-.591.162-.89-.22-.64-.713-.261-.798.127-.252.874.019 2.077.521 3.015.175.299.417.725.722.776h.01l.003.006Z" />
        </G>
        <Path
          fill="#8D6A00"
          d="M7.545 100.045c-.27.309-.563.611-.939.783-.133.063-.296.127-.334.27-.041.162.124.318.29.328.165.009.32-.077.46-.169 1.057-.7 1.795-1.771 2.479-2.834.311-.487.658-.999.836-1.552.194-.614-.391-.258-.576.032-.687 1.084-1.367 2.172-2.216 3.142Z"
        />
        <Path
          fill="#8D6A00"
          d="M7.557 98.519c.652.906 1.323 1.832 2.24 2.474.031.08-.07.146-.156.159-.34.045-.65-.191-.9-.426-1.114-1.04-1.963-2.325-2.8-3.597-.098-.15-.464-.668-.32-.86.219-.295.55.322.639.446.432.601.865 1.202 1.297 1.807v-.003ZM80.502 97.978c.856.626 1.988.658 3.048.706.133.006.286.022.362.133-.13.226-.436.261-.693.261-.897 0-1.804-.12-2.621-.486s-1.54-.996-1.896-1.817c-.092-.21-.159-.448-.082-.664.28-.815.703.324.826.553.27.496.599.977 1.06 1.314h-.004Z"
        />
        <Path
          fill="#8D6A00"
          d="M80.827 99.164c-.519.566-1.1 1.164-1.86 1.269.063.156.235.242.403.271.554.089 1.056-.312 1.457-.706a16.802 16.802 0 0 0 2.503-3.124c.111-.184.398-.537.318-.766-.06-.166-.283-.242-.423-.153-.172.108-.327.607-.442.792-.172.273-.36.54-.553.798-.433.567-.916 1.098-1.397 1.622l-.006-.003Z"
        />
        <Path
          fill="#8D6A00"
          d="M80.188 100.538a1.315 1.315 0 0 1-.376-.07c-.225-.086-.397-.264-.617-.359a.358.358 0 0 0-.168-.035c-.137.016-.204.165-.194.289.022.28.28.497.547.64.509.276 1.12.394 1.708.337.69-.07 1.326-.372 1.927-.687.207-.108.42-.223.57-.391.155-.175.365-.605.15-.805-.176-.162-.332.114-.446.229-.449.452-1.228.69-1.874.801-.404.07-.817.089-1.227.054v-.003Z"
        />
        <Image
          x={20}
          y={16}
          id="image0_367_1053"
          width={44.27}
          height={71.98}
          preserveAspectRatio="xMidYMid slice"
          href={href}
        />
      </Svg>
    </TouchableOpacity>
  );
};

// Main component with toggle
const USE_SKIA = true; // Toggle for performance testing

export const RolePlayCard = (props: Props) => {
  if (USE_SKIA && SkiaAvailable) {
    return <RolePlayCardSkia {...props} />;
  }
  return <RolePlayCardSVG {...props} />;
};
