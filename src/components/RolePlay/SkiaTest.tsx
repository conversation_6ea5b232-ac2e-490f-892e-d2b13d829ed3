import React from 'react';
import {View, Text} from 'react-native';

// Test Skia import
let SkiaAvailable = false;
let Canvas, Circle;

try {
  const Skia = require('@shopify/react-native-skia');
  Canvas = Skia.Canvas;
  Circle = Skia.Circle;
  SkiaAvailable = true;
  console.log('✅ Skia imported successfully');
} catch (error) {
  console.log('❌ Skia import failed:', error.message);
  SkiaAvailable = false;
}

export const SkiaTest = () => {
  if (!SkiaAvailable) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <Text>Skia not available</Text>
      </View>
    );
  }

  return (
    <View style={{flex: 1}}>
      <Text>Testing Skia Canvas...</Text>
      <Canvas style={{flex: 1, backgroundColor: 'lightblue'}}>
        <Circle cx={100} cy={100} r={50} color="red" />
      </Canvas>
    </View>
  );
};
