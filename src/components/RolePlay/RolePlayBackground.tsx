import React, {memo, useMemo} from 'react';
import Animated, {useAnimatedProps, SharedValue} from 'react-native-reanimated';
import Svg, {G, Image as SvgImage} from 'react-native-svg';
import {Theme} from '../../themes';

const AnimatedSvgImage = Animated.createAnimatedComponent(SvgImage);

interface RolePlayBackgroundProps {
  modalContent: RolePlayCard | null;
  trasnY: SharedValue<number>;
  opacity: SharedValue<number>;
}

export const RolePlayBackground: React.FC<RolePlayBackgroundProps> = memo(
  ({modalContent, trasnY, opacity}) => {
    const animatedProps = useAnimatedProps(
      () => ({
        y: trasnY.value,
      }),
      [trasnY],
    );

    const animatedStyle = useAnimatedProps(
      () => ({
        opacity: opacity.value,
      }),
      [opacity],
    );

    const characterImageSource = useMemo(
      () =>
        (Theme.images as any)[modalContent?.rlFigureCode || ''] ??
        Theme.images.rolePlayCharactorDefault,
      [modalContent?.rlFigureCode],
    );

    const staticElements = useMemo(
      () => (
        <>
          <G id="blur-layer">
            <SvgImage
              x={0}
              y={0}
              id="image0_367_1053"
              width={375}
              height={812}
              preserveAspectRatio="none"
              href={Theme.images.rolePlayBlur}
            />
          </G>
          <G id="animated-name">
            <AnimatedSvgImage
              x={0}
              y={0}
              id="image0_367_1053"
              width={375}
              height={248.16}
              preserveAspectRatio="none"
              href={Theme.images.rolePlayName}
              animatedProps={animatedProps}
            />
          </G>
          <G id="background">
            <SvgImage
              x={0}
              y={0}
              id="image0_367_1053"
              width={375}
              height={812}
              preserveAspectRatio="xMidYMid slice"
              href={Theme.images.bgRolePlay}
            />
          </G>
        </>
      ),
      [],
    );

    return (
      <Svg
        width={'100%'}
        height={'100%'}
        viewBox="0 0 375 812"
        preserveAspectRatio="none"
        fill="none">
        <G>
          {staticElements}
          <G id="animated-character">
            <AnimatedSvgImage
              x={115.51}
              y={310.3}
              id="image0_367_1053"
              width={143.97}
              height={236.7}
              preserveAspectRatio="none"
              href={characterImageSource}
              animatedProps={animatedStyle}
            />
          </G>
        </G>
      </Svg>
    );
  },
);
