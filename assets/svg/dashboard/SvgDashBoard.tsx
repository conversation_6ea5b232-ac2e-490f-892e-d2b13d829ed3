import React, {memo, useCallback} from 'react';
import {Canvas, Image, useImage} from '@shopify/react-native-skia';
import {Dimensions, View} from 'react-native';
import {Gesture, GestureDetector} from 'react-native-gesture-handler';
import {heightScreen, widthScreen} from '../../../src/utils/Scale';

const {width, height} = Dimensions.get('screen');

interface ActionProps {
  onPressMyClass: () => void;
  onPressMission: () => void;
  onPressPractice: () => void;
  onPressLeaderBoard: () => void;
  onPressFunTalk: () => void;
}

const SvgDashboard: React.FC<ActionProps> = ({
  onPressPractice,
  onPressMission,
  onPressLeaderBoard,
  onPressFunTalk,
  onPressMyClass,
}) => {
  const bgMainImage = useImage(require('./bgMain.png'));
  const mySchoolImage = useImage(require('./mySchool.png'));
  const missionImage = useImage(require('./mission.png'));
  const practiceImage = useImage(require('./practice.png'));
  const leaderBoardImage = useImage(require('./leaderBoard.png'));
  const funtalkImage = useImage(require('./funtalk.png'));

  if (
    !bgMainImage ||
    !mySchoolImage ||
    !missionImage ||
    !practiceImage ||
    !leaderBoardImage ||
    !funtalkImage
  ) {
    return null;
  }

  const scaleX = widthScreen / 1125;
  const scaleY = heightScreen / 2436;

  // Define touch areas with scaled coordinates
  const touchAreas = [
    {
      name: 'mySchool',
      x: 329.05 * scaleX,
      y: 493.762 * scaleY,
      width: 793.44 * scaleX,
      height: 438.72 * scaleY,
      onPress: onPressMyClass,
    },
    {
      name: 'mission',
      x: 511.69 * scaleX,
      y: 1028.04 * scaleY,
      width: 620.34 * scaleX,
      height: 430.32 * scaleY,
      onPress: onPressMission,
    },
    {
      name: 'practice',
      x: 20.55 * scaleX,
      y: 1390.49 * scaleY,
      width: 560.16 * scaleX,
      height: 441.6 * scaleY,
      onPress: onPressPractice,
    },
    {
      name: 'leaderBoard',
      x: 67.69 * scaleX,
      y: 750.922 * scaleY,
      width: 330.96 * scaleX,
      height: 463.44 * scaleY,
      onPress: onPressLeaderBoard,
    },
    {
      name: 'funtalk',
      x: 247.09 * scaleX,
      y: 1756.52 * scaleY,
      width: 776.64 * scaleX,
      height: 502.8 * scaleY,
      onPress: onPressFunTalk,
    },
  ];

  // Check if touch point is within an area
  const isPointInArea = useCallback((x: number, y: number, area: typeof touchAreas[0]) => {
    return (
      x >= area.x &&
      x <= area.x + area.width &&
      y >= area.y &&
      y <= area.y + area.height
    );
  }, []);

  // Handle tap gesture
  const tapGesture = Gesture.Tap()
    .onEnd((event) => {
      const {x, y} = event;

      // Find which area was tapped
      for (const area of touchAreas) {
        if (isPointInArea(x, y, area)) {
          console.log(`Tapped on ${area.name}`);
          area.onPress();
          break;
        }
      }
    });

  return (
    <View style={{flex: 1}}>
      <Canvas style={{width: widthScreen, height: heightScreen}}>
        <Image
          image={bgMainImage}
          x={0}
          y={0}
          width={widthScreen}
          height={heightScreen}
          fit="cover"
        />

        <Image
          image={mySchoolImage}
          x={329.05 * scaleX}
          y={493.762 * scaleY}
          width={793.44 * scaleX}
          height={438.72 * scaleY}
          fit="fill"
        />

        <Image
          image={missionImage}
          x={511.69 * scaleX}
          y={1028.04 * scaleY}
          width={620.34 * scaleX}
          height={430.32 * scaleY}
          fit="fill"
        />

        <Image
          image={practiceImage}
          x={20.55 * scaleX}
          y={1390.49 * scaleY}
          width={560.16 * scaleX}
          height={441.6 * scaleY}
          fit="fill"
        />

        <Image
          image={leaderBoardImage}
          x={67.69 * scaleX}
          y={750.922 * scaleY}
          width={330.96 * scaleX}
          height={463.44 * scaleY}
          fit="fill"
        />

        <Image
          image={funtalkImage}
          x={247.09 * scaleX}
          y={1756.52 * scaleY}
          width={776.64 * scaleX}
          height={502.8 * scaleY}
          fit="fill"
        />
      </Canvas>

      {/* <Pressable
        style={{
          position: 'absolute',
          left: 329.05 * scaleX,
          top: 493.762 * scaleY,
          width: 793.44 * scaleX,
          height: 438.72 * scaleY,
        }}
        onPress={onPressMyClass}
      />

      <Pressable
        style={{
          position: 'absolute',
          left: 511.69 * scaleX,
          top: 1028.04 * scaleY,
          width: 620.34 * scaleX,
          height: 430.32 * scaleY,
        }}
        onPress={onPressMission}
      />

      <Pressable
        style={{
          position: 'absolute',
          left: 20.55 * scaleX,
          top: 1390.49 * scaleY,
          width: 560.16 * scaleX,
          height: 441.6 * scaleY,
        }}
        onPress={onPressPractice}
      />

      <Pressable
        style={{
          position: 'absolute',
          left: 67.69 * scaleX,
          top: 750.922 * scaleY,
          width: 330.96 * scaleX,
          height: 463.44 * scaleY,
        }}
        onPress={onPressLeaderBoard}
      />

      <Pressable
        style={{
          position: 'absolute',
          left: 247.09 * scaleX,
          top: 1756.52 * scaleY,
          width: 776.64 * scaleX,
          height: 502.8 * scaleY,
        }}
        onPress={onPressFunTalk}
      /> */}
    </View>
  );
};

export default memo(SvgDashboard);
