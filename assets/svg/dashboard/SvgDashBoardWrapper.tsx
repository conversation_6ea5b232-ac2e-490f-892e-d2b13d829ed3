import React from 'react';
import SvgDashboard from './SvgDashBoard';

// Toggle để test performance
const USE_SKIA = false; // Set true để test Skia version

interface ActionProps {
  onPressMyClass: () => void;
  onPressMission: () => void;
  onPressPractice: () => void;
  onPressLeaderBoard: () => void;
  onPressFunTalk: () => void;
}

const SvgDashboardWrapper: React.FC<ActionProps> = (props) => {
  if (USE_SKIA) {
    try {
      // Dynamic import Skia version
      const SvgDashboardSkia = require('./SvgDashBoardSkia').default;
      return <SvgDashboardSkia {...props} />;
    } catch (error) {
      console.warn('Skia Dashboard failed, falling back to SVG:', error);
      return <SvgDashboard {...props} />;
    }
  }
  
  return <SvgDashboard {...props} />;
};

export default React.memo(SvgDashboardWrapper);
